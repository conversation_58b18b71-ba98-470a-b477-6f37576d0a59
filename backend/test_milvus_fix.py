#!/usr/bin/env python3
"""
测试Milvus索引类型修复

验证RAG系统能否正常初始化并创建向量索引
"""
import asyncio
import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from app.core.config import get_settings


def setup_test_logging():
    """设置测试日志"""
    logger.remove()  # 移除默认处理器
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> - <level>{message}</level>",
        level="DEBUG",
        colorize=True
    )


async def test_milvus_compatibility():
    """测试Milvus兼容性"""
    logger.info("开始测试Milvus兼容性...")
    
    try:
        # 1. 测试兼容性管理器
        logger.info("1. 测试兼容性管理器")
        from app.rag.milvus_compatibility import get_compatibility_manager, print_compatibility_report
        
        manager = get_compatibility_manager()
        print_compatibility_report()
        
        # 2. 测试向量存储连接
        logger.info("2. 测试向量存储连接")
        from app.rag.vector_store import MilvusVectorStore
        
        vector_store = MilvusVectorStore()
        
        # 连接测试
        logger.info("尝试连接Milvus...")
        vector_store.connect()
        logger.info("✅ Milvus连接成功")
        
        # 3. 测试集合创建和索引
        logger.info("3. 测试集合创建和索引")
        
        # 删除现有集合（如果存在）
        success = vector_store.initialize_collection(drop_existing=True)
        if success:
            logger.info("✅ 集合创建和索引成功")
        else:
            logger.warning("⚠️ 集合创建部分成功")
        
        # 4. 测试基本操作
        logger.info("4. 测试基本向量操作")
        
        # 测试数据
        test_embedding = [0.1] * 768  # 768维向量
        test_chunk = {
            "chunk_id": "test_chunk_001",
            "text": "这是一个测试文档块，用于验证Milvus索引修复是否成功。",
            "source": "test_document.txt",
            "metadata": '{"test": true}',
            "embedding": test_embedding
        }
        
        # 添加向量
        result = vector_store.add_vectors([test_chunk])
        if result:
            logger.info("✅ 向量添加成功")
        else:
            logger.warning("⚠️ 向量添加失败")
        
        # 搜索测试
        search_results = vector_store.search_vectors(
            query_vector=test_embedding,
            top_k=1
        )
        
        if search_results:
            logger.info(f"✅ 向量搜索成功，找到 {len(search_results)} 个结果")
            for i, result in enumerate(search_results):
                logger.info(f"  结果 {i+1}: 相似度={result.get('score', 0):.4f}")
        else:
            logger.warning("⚠️ 向量搜索无结果")
        
        # 5. 清理测试
        logger.info("5. 清理测试资源")
        vector_store.disconnect()
        logger.info("✅ 资源清理完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        logger.error(f"错误详情:\n{traceback.format_exc()}")
        
        # 尝试错误诊断
        try:
            from app.rag.milvus_compatibility import diagnose_milvus_error
            diagnosis = diagnose_milvus_error(str(e))
            
            logger.error("🔍 错误诊断:")
            logger.error(f"  错误类型: {diagnosis['error_type']}")
            logger.error(f"  建议修复: {diagnosis['suggested_fix']}")
            if diagnosis['recommended_index_type']:
                logger.error(f"  推荐索引: {diagnosis['recommended_index_type']}")
                
        except Exception as diag_error:
            logger.debug(f"错误诊断失败: {diag_error}")
        
        return False


async def test_rag_system_initialization():
    """测试RAG系统完整初始化"""
    logger.info("开始测试RAG系统完整初始化...")
    
    try:
        from app.rag.rag_system import RAGSystem
        
        # 创建RAG系统
        rag_system = RAGSystem()
        
        # 初始化
        logger.info("初始化RAG系统...")
        await rag_system.initialize()
        logger.info("✅ RAG系统初始化成功")
        
        # 健康检查
        logger.info("执行健康检查...")
        health = await rag_system.health_check()
        
        logger.info(f"系统状态: {health.get('status', 'unknown')}")
        components = health.get("components", {})
        for name, status in components.items():
            component_status = status.get("status", "unknown")
            message = status.get("message", "")
            if component_status == "healthy":
                logger.info(f"  ✅ {name}: {component_status} - {message}")
            else:
                logger.warning(f"  ⚠️ {name}: {component_status} - {message}")
        
        # 简单功能测试
        logger.info("测试基本功能...")
        
        # 添加测试文本
        result = await rag_system.add_text_directly(
            "这是一个测试文档，用于验证RAG系统的基本功能。包含一些关键词：自动化测试、向量搜索、文档处理。",
            "test_doc_001"
        )
        
        if result.success:
            logger.info("✅ 文本添加成功")
        else:
            logger.warning(f"⚠️ 文本添加失败: {result.message}")
        
        # 搜索测试
        search_response = await rag_system.search("自动化测试")
        
        if search_response.success and search_response.retrieved_chunks:
            logger.info(f"✅ 搜索成功，找到 {len(search_response.retrieved_chunks)} 个结果")
        else:
            logger.warning("⚠️ 搜索无结果")
        
        # 清理
        await rag_system.cleanup()
        logger.info("✅ RAG系统测试完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ RAG系统测试失败: {e}")
        logger.error(f"错误详情:\n{traceback.format_exc()}")
        return False


async def main():
    """主测试函数"""
    setup_test_logging()
    
    logger.info("🚀 开始Milvus索引类型修复验证")
    logger.info("="*60)
    
    # 检查配置
    settings = get_settings()
    if not settings.RAG_ENABLED:
        logger.error("❌ RAG系统未启用，请设置 RAG_ENABLED=true")
        return 1
    
    success_count = 0
    total_tests = 2
    
    # 测试1: Milvus兼容性
    logger.info("\n📋 测试1: Milvus兼容性和基本操作")
    logger.info("-" * 40)
    if await test_milvus_compatibility():
        success_count += 1
        logger.info("✅ 测试1通过")
    else:
        logger.error("❌ 测试1失败")
    
    # 测试2: RAG系统初始化
    logger.info("\n📋 测试2: RAG系统完整初始化")
    logger.info("-" * 40)
    if await test_rag_system_initialization():
        success_count += 1
        logger.info("✅ 测试2通过")
    else:
        logger.error("❌ 测试2失败")
    
    # 总结
    logger.info("\n" + "="*60)
    logger.info("🏁 测试总结")
    logger.info(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！Milvus索引类型问题已修复")
        logger.info("💡 现在可以正常启动UI自动化测试项目的后端服务")
        return 0
    else:
        logger.error("❌ 部分测试失败，请检查错误信息并进行修复")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
