#!/usr/bin/env python3
"""
简化的Milvus索引类型测试

直接测试Milvus兼容性，不依赖完整的RAG系统
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_milvus_compatibility_simple():
    """简化的Milvus兼容性测试"""
    print("🚀 开始简化的Milvus兼容性测试")
    print("="*50)
    
    try:
        # 1. 测试pymilvus导入
        print("1. 测试pymilvus导入...")
        try:
            import pymilvus
            from pymilvus import __version__ as pymilvus_version
            print(f"✅ PyMilvus导入成功，版本: {pymilvus_version}")
        except ImportError as e:
            print(f"❌ PyMilvus导入失败: {e}")
            return False
        
        # 2. 测试兼容性管理器
        print("\n2. 测试兼容性管理器...")
        try:
            from app.rag.milvus_compatibility import MilvusCompatibilityManager
            
            manager = MilvusCompatibilityManager()
            
            # 获取兼容性信息
            scalar_types = manager.get_compatible_scalar_index_types()
            vector_types = manager.get_compatible_vector_index_types()
            
            print(f"✅ 兼容性管理器创建成功")
            print(f"   支持的标量索引类型: {scalar_types}")
            print(f"   支持的向量索引类型: {vector_types}")
            print(f"   推荐标量索引: {manager.get_recommended_scalar_index_type()}")
            print(f"   推荐向量索引: {manager.get_recommended_vector_index_type()}")
            
        except Exception as e:
            print(f"❌ 兼容性管理器测试失败: {e}")
            return False
        
        # 3. 测试错误诊断
        print("\n3. 测试错误诊断...")
        try:
            test_error = "MilvusException: (code=1100, message=index type not match: invalid parameter[expected=Trie][actual=TRIE])"
            diagnosis = manager.diagnose_index_error(test_error)
            
            print(f"✅ 错误诊断成功")
            print(f"   错误类型: {diagnosis['error_type']}")
            print(f"   建议修复: {diagnosis['suggested_fix']}")
            print(f"   推荐索引: {diagnosis['recommended_index_type']}")
            
        except Exception as e:
            print(f"❌ 错误诊断测试失败: {e}")
            return False
        
        # 4. 测试Milvus连接（如果可用）
        print("\n4. 测试Milvus连接...")
        try:
            from pymilvus import connections, utility
            
            # 尝试连接到本地Milvus
            try:
                connections.connect(
                    alias="test",
                    host="127.0.0.1",
                    port=19530
                )
                
                # 获取服务器版本
                server_version = utility.get_server_version()
                print(f"✅ Milvus连接成功，服务器版本: {server_version}")
                
                # 断开连接
                connections.disconnect("test")
                
            except Exception as conn_error:
                print(f"⚠️ Milvus连接失败（这是正常的，如果没有运行Milvus服务器）: {conn_error}")
                # 连接失败不影响整体测试结果
                
        except Exception as e:
            print(f"❌ Milvus连接测试失败: {e}")
            return False
        
        print("\n" + "="*50)
        print("🎉 简化测试完成！")
        print("💡 Milvus兼容性修复已就绪")
        print("📝 主要修复内容:")
        print("   - 索引类型兼容性检查")
        print("   - 自动回退机制")
        print("   - 错误诊断功能")
        print("   - 版本适配支持")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")
        return False


def show_fix_summary():
    """显示修复总结"""
    print("\n" + "🔧 Milvus索引类型错误修复总结")
    print("="*60)
    
    print("\n📋 问题分析:")
    print("   原始错误: index type not match: invalid parameter[expected=Trie][actual=TRIE]")
    print("   根本原因: 不同Milvus版本对索引类型大小写敏感性不同")
    
    print("\n🛠️ 修复方案:")
    print("   1. 创建兼容性管理器 (milvus_compatibility.py)")
    print("   2. 实现索引类型自动检测和回退机制")
    print("   3. 更新向量存储模块使用兼容性检查")
    print("   4. 添加错误诊断和建议功能")
    
    print("\n📁 修改的文件:")
    print("   - backend/app/rag/milvus_compatibility.py (新增)")
    print("   - backend/app/rag/vector_store.py (更新)")
    print("   - backend/test_milvus_fix.py (测试脚本)")
    print("   - backend/test_milvus_simple.py (简化测试)")
    
    print("\n🎯 修复效果:")
    print("   ✅ 自动适配不同Milvus版本的索引类型")
    print("   ✅ 提供多种索引类型回退选项")
    print("   ✅ 智能错误诊断和修复建议")
    print("   ✅ 保证RAG系统正常初始化")
    
    print("\n🚀 下一步:")
    print("   1. 确保Milvus服务器正在运行")
    print("   2. 启动UI自动化测试项目后端服务")
    print("   3. 验证RAG系统正常工作")


if __name__ == "__main__":
    success = test_milvus_compatibility_simple()
    
    if success:
        show_fix_summary()
        print(f"\n✅ 测试成功！可以尝试启动后端服务了。")
        sys.exit(0)
    else:
        print(f"\n❌ 测试失败，请检查错误信息。")
        sys.exit(1)
