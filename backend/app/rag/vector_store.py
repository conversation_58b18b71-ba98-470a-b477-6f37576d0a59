"""
向量存储服务模块

提供Milvus向量数据库的存储和管理功能
"""
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from pymilvus import (
    connections, Collection, CollectionSchema, FieldSchema, DataType,
    utility, Index
)
from loguru import logger

from .config import get_rag_config
from .document_processor import DocumentChunk


class VectorStoreService:
    """向量存储服务 - 集成到UI自动化测试项目"""
    
    def __init__(self, config=None):
        self.config = config or get_rag_config().milvus
        self.collection = None
        self._connected = False
        self._initialized = False
        
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
        
    def connect(self):
        """连接到Milvus服务器"""
        if self._connected:
            return
            
        try:
            connections.connect(
                alias="default",
                host=self.config.host,
                port=self.config.port
            )
            self._connected = True
            logger.info(f"成功连接到Milvus服务器: {self.config.host}:{self.config.port}")
            
            # 检查服务器状态
            server_version = utility.get_server_version()
            if server_version:
                logger.info(f"Milvus服务器版本: {server_version}")
                
        except Exception as e:
            logger.error(f"连接Milvus失败: {e}")
            raise
            
    def disconnect(self):
        """断开连接"""
        if self._connected:
            connections.disconnect("default")
            self._connected = False
            logger.info("已断开Milvus连接")
            
    def initialize_collection(self, drop_existing: bool = False) -> bool:
        """初始化集合"""
        if not self._connected:
            self.connect()
            
        collection_name = self.config.collection_name
        
        # 检查集合是否存在
        if utility.has_collection(collection_name):
            if drop_existing:
                logger.info(f"删除现有集合: {collection_name}")
                utility.drop_collection(collection_name)
            else:
                logger.info(f"集合 {collection_name} 已存在，直接使用")
                self.collection = Collection(collection_name)
                self._initialized = True
                return True
                
        try:
            # 定义字段
            fields = [
                FieldSchema(
                    name="id",
                    dtype=DataType.INT64,
                    is_primary=True,
                    auto_id=True,
                    description="主键ID"
                ),
                FieldSchema(
                    name="chunk_id",
                    dtype=DataType.VARCHAR,
                    max_length=64,
                    description="文档块ID"
                ),
                FieldSchema(
                    name="text",
                    dtype=DataType.VARCHAR,
                    max_length=65535,
                    description="原始文本内容"
                ),
                FieldSchema(
                    name="embedding",
                    dtype=DataType.FLOAT_VECTOR,
                    dim=self.config.dimension,
                    description="文本嵌入向量"
                ),
                FieldSchema(
                    name="metadata",
                    dtype=DataType.JSON,
                    description="元数据信息"
                ),
                FieldSchema(
                    name="source",
                    dtype=DataType.VARCHAR,
                    max_length=1000,
                    description="文档源路径"
                ),
                FieldSchema(
                    name="timestamp",
                    dtype=DataType.INT64,
                    description="创建时间戳"
                )
            ]
            
            # 创建集合模式
            schema = CollectionSchema(
                fields=fields,
                description=f"UI自动化测试项目RAG向量集合",
                enable_dynamic_field=True
            )
            
            # 创建集合
            self.collection = Collection(
                name=collection_name,
                schema=schema,
                using='default'
            )
            
            logger.info(f"成功创建集合: {collection_name}")
            
            # 创建索引
            self._create_index_with_retry()
            self._initialized = True

            return True

        except Exception as e:
            logger.error(f"创建集合失败: {e}")
            raise

    def _create_index_with_retry(self, max_retries: int = 3):
        """带重试的索引创建"""
        for attempt in range(max_retries):
            try:
                self._create_index()
                return
            except Exception as e:
                logger.warning(f"索引创建尝试 {attempt + 1}/{max_retries} 失败: {e}")
                if attempt == max_retries - 1:
                    logger.error("索引创建最终失败，但集合已创建，系统可以继续运行")
                    # 不抛出异常，允许系统继续运行
                    return
                time.sleep(2 ** attempt)  # 指数退避
            
    def _create_index(self):
        """创建向量索引"""
        try:
            # 检查是否已有索引
            if self.collection.has_index():
                logger.info("集合已有索引，跳过创建")
                return

            # 创建向量索引
            index_params = {
                "metric_type": self.config.metric_type,
                "index_type": self.config.index_type,
                "params": {"nlist": self.config.nlist}
            }

            logger.info(f"创建向量索引: {index_params}")
            self.collection.create_index(
                field_name="embedding",
                index_params=index_params
            )

            # 创建标量字段索引 - 使用兼容的索引类型
            self._create_scalar_index("chunk_id")
            self._create_scalar_index("source")

            logger.info("向量索引创建成功")

        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            raise

    def _create_scalar_index(self, field_name: str):
        """创建标量字段索引，自动处理索引类型兼容性"""
        try:
            # 尝试不同的索引类型，按优先级排序
            index_types = ["Trie", "TRIE", "STL_SORT"]

            for index_type in index_types:
                try:
                    logger.debug(f"尝试为字段 {field_name} 创建 {index_type} 索引")
                    self.collection.create_index(
                        field_name=field_name,
                        index_params={"index_type": index_type}
                    )
                    logger.info(f"成功为字段 {field_name} 创建 {index_type} 索引")
                    return
                except Exception as e:
                    logger.debug(f"索引类型 {index_type} 失败: {e}")
                    continue

            # 如果所有索引类型都失败，记录警告但不抛出异常
            logger.warning(f"无法为字段 {field_name} 创建索引，将使用默认配置")

        except Exception as e:
            logger.warning(f"创建标量索引失败: {e}")
            # 标量索引失败不应阻止系统运行
            
    def load_collection(self):
        """加载集合到内存"""
        if not self.collection:
            raise ValueError("集合未创建或未连接")
            
        try:
            self.collection.load()
            logger.info(f"集合 {self.config.collection_name} 已加载到内存")
            
        except Exception as e:
            logger.error(f"加载集合失败: {e}")
            raise
            
    def insert_chunks(self, chunks: List[DocumentChunk], embeddings: List[List[float]]) -> List[int]:
        """插入文档块到集合"""
        if not self._initialized:
            raise ValueError("向量存储未初始化")
            
        if len(chunks) != len(embeddings):
            raise ValueError("文档块和嵌入向量数量不匹配")
            
        try:
            # 准备数据
            current_time = int(time.time() * 1000)  # 毫秒时间戳
            
            data = []
            for chunk, embedding in zip(chunks, embeddings):
                data.append({
                    "chunk_id": chunk.chunk_id,
                    "text": chunk.text,
                    "embedding": embedding,
                    "metadata": chunk.metadata,
                    "source": chunk.source,
                    "timestamp": current_time
                })
                
            # 插入数据
            logger.info(f"插入 {len(data)} 条数据到集合")
            insert_result = self.collection.insert(data)
            
            # 刷新数据
            self.collection.flush()
            
            logger.info(f"数据插入成功，ID范围: {insert_result.primary_keys[0]} - {insert_result.primary_keys[-1]}")
            return insert_result.primary_keys
            
        except Exception as e:
            logger.error(f"插入数据失败: {e}")
            raise
            
    def search_similar(self, query_embedding: List[float], top_k: int = 5,
                      filter_expr: Optional[str] = None) -> List[Dict[str, Any]]:
        """搜索相似向量"""
        if not self._initialized:
            raise ValueError("向量存储未初始化")
            
        try:
            # 确保集合已加载
            if not self.collection.has_index():
                self.load_collection()
                
            # 搜索参数
            search_params = {
                "metric_type": self.config.metric_type,
                "params": {"nprobe": self.config.nprobe}
            }
            
            # 执行搜索
            logger.debug(f"搜索相似向量，top_k: {top_k}")
            start_time = time.time()
            
            results = self.collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                expr=filter_expr,
                output_fields=["chunk_id", "text", "metadata", "source", "timestamp"]
            )
            
            search_time = time.time() - start_time
            logger.debug(f"搜索完成，耗时: {search_time:.3f}s")
            
            # 处理结果
            formatted_results = []
            for hit in results[0]:
                formatted_results.append({
                    "id": hit.id,
                    "chunk_id": hit.entity.get("chunk_id"),
                    "text": hit.entity.get("text"),
                    "metadata": hit.entity.get("metadata", {}),
                    "source": hit.entity.get("source"),
                    "timestamp": hit.entity.get("timestamp"),
                    "similarity_score": float(hit.score),
                    "distance": float(hit.distance) if hasattr(hit, 'distance') else None
                })
                
            logger.info(f"找到 {len(formatted_results)} 个相似结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            raise

    def search_by_chunk_id(self, chunk_id: str) -> Optional[Dict[str, Any]]:
        """根据块ID搜索"""
        try:
            filter_expr = f'chunk_id == "{chunk_id}"'
            results = self.collection.query(
                expr=filter_expr,
                output_fields=["chunk_id", "text", "metadata", "source", "timestamp"]
            )

            if results:
                return results[0]
            return None

        except Exception as e:
            logger.error(f"根据块ID搜索失败: {e}")
            raise

    def search_by_source(self, source: str, limit: int = 100) -> List[Dict[str, Any]]:
        """根据源文件搜索"""
        try:
            filter_expr = f'source == "{source}"'
            results = self.collection.query(
                expr=filter_expr,
                limit=limit,
                output_fields=["chunk_id", "text", "metadata", "source", "timestamp"]
            )

            return results

        except Exception as e:
            logger.error(f"根据源文件搜索失败: {e}")
            raise

    def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        if not self.collection:
            return {}

        try:
            stats = self.collection.get_stats()
            return {
                "collection_name": self.config.collection_name,
                "row_count": stats.get("row_count", 0),
                "data_size": stats.get("data_size", 0),
                "index_size": stats.get("index_size", 0),
                "has_index": self.collection.has_index(),
                "schema": str(self.collection.schema),
                "is_loaded": self.collection.is_loaded if hasattr(self.collection, 'is_loaded') else False
            }

        except Exception as e:
            logger.error(f"获取集合统计信息失败: {e}")
            return {}

    def delete_by_chunk_ids(self, chunk_ids: List[str]) -> int:
        """根据块ID删除数据"""
        if not chunk_ids:
            return 0

        try:
            # 构建删除表达式
            chunk_ids_str = '", "'.join(chunk_ids)
            expr = f'chunk_id in ["{chunk_ids_str}"]'

            result = self.collection.delete(expr)
            self.collection.flush()

            delete_count = result.delete_count if hasattr(result, 'delete_count') else len(chunk_ids)
            logger.info(f"删除数据成功，删除条数: {delete_count}")
            return delete_count

        except Exception as e:
            logger.error(f"删除数据失败: {e}")
            raise

    def delete_by_source(self, source: str) -> int:
        """根据源文件删除数据"""
        try:
            expr = f'source == "{source}"'
            result = self.collection.delete(expr)
            self.collection.flush()

            delete_count = result.delete_count if hasattr(result, 'delete_count') else 0
            logger.info(f"删除源文件 {source} 的数据成功，删除条数: {delete_count}")
            return delete_count

        except Exception as e:
            logger.error(f"删除源文件数据失败: {e}")
            raise

    def clear_collection(self):
        """清空集合数据"""
        try:
            # 删除所有数据
            expr = "id >= 0"  # 匹配所有记录
            self.collection.delete(expr)
            self.collection.flush()

            logger.info("集合数据清空完成")

        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            raise

    def drop_collection(self):
        """删除集合"""
        if utility.has_collection(self.config.collection_name):
            utility.drop_collection(self.config.collection_name)
            logger.info(f"集合 {self.config.collection_name} 已删除")
            self.collection = None
            self._initialized = False

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self._connected:
                self.connect()

            # 检查连接状态
            server_version = utility.get_server_version()

            # 检查集合状态
            collection_exists = utility.has_collection(self.config.collection_name)

            stats = {}
            if collection_exists and self.collection:
                stats = self.get_collection_stats()

            return {
                "status": "healthy",
                "server_version": server_version,
                "host": self.config.host,
                "port": self.config.port,
                "collection_name": self.config.collection_name,
                "collection_exists": collection_exists,
                "collection_stats": stats,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }


# 便捷函数
def create_vector_store() -> VectorStoreService:
    """创建并连接向量存储服务"""
    store = VectorStoreService()
    store.connect()
    return store


def batch_insert_documents(chunks: List[DocumentChunk], embeddings: List[List[float]]) -> List[int]:
    """批量插入文档"""
    with VectorStoreService() as store:
        store.initialize_collection()
        store.load_collection()
        return store.insert_chunks(chunks, embeddings)


def search_similar_documents(query_embedding: List[float], top_k: int = 5) -> List[Dict[str, Any]]:
    """搜索相似文档"""
    with VectorStoreService() as store:
        if not store._initialized:
            store.initialize_collection()
        store.load_collection()
        return store.search_similar(query_embedding, top_k)


if __name__ == "__main__":
    # 测试代码
    with VectorStoreService() as store:
        # 初始化集合
        store.initialize_collection(drop_existing=True)

        # 创建测试数据
        from .document_processor import DocumentChunk

        test_chunks = [
            DocumentChunk(
                text="这是第一个测试文档块",
                metadata={"source": "test1.txt", "chunk_index": 0},
                chunk_id="test_chunk_1",
                source="test1.txt"
            ),
            DocumentChunk(
                text="这是第二个测试文档块",
                metadata={"source": "test2.txt", "chunk_index": 0},
                chunk_id="test_chunk_2",
                source="test2.txt"
            )
        ]

        # 模拟嵌入向量
        test_embeddings = [[0.1] * 768, [0.2] * 768]

        # 插入数据
        ids = store.insert_chunks(test_chunks, test_embeddings)
        print(f"插入数据ID: {ids}")

        # 加载集合
        store.load_collection()

        # 搜索测试
        query_embedding = [0.15] * 768
        results = store.search_similar(query_embedding, top_k=2)
        print(f"搜索结果: {len(results)} 条")
        for result in results:
            print(f"  文本: {result['text']}")
            print(f"  相似度: {result['similarity_score']}")

        # 统计信息
        stats = store.get_collection_stats()
        print(f"集合统计: {stats}")

        # 健康检查
        health = store.health_check()
        print(f"健康检查: {health['status']}")
