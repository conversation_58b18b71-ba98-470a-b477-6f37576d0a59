"""
Milvus兼容性检查和修复工具

解决不同Milvus版本之间的索引类型兼容性问题
"""
import re
from typing import Dict, List, Optional, Tuple
from loguru import logger

try:
    from pymilvus import __version__ as pymilvus_version, utility
    PYMILVUS_AVAILABLE = True
except ImportError:
    pymilvus_version = "unknown"
    utility = None
    PYMILVUS_AVAILABLE = False


class MilvusCompatibilityManager:
    """Milvus兼容性管理器"""
    
    # 不同版本支持的索引类型映射
    INDEX_TYPE_COMPATIBILITY = {
        # 标量字段索引类型
        "scalar": {
            "2.3.x": ["Trie", "STL_SORT"],
            "2.2.x": ["TRIE", "STL_SORT"], 
            "2.1.x": ["TRIE"],
            "2.0.x": ["TRIE"],
            "default": ["Trie", "TRIE", "STL_SORT"]  # 按优先级排序
        },
        # 向量字段索引类型
        "vector": {
            "2.3.x": ["IVF_FLAT", "IVF_SQ8", "IVF_PQ", "HNSW", "SCANN"],
            "2.2.x": ["IVF_FLAT", "IVF_SQ8", "IVF_PQ", "HNSW"],
            "2.1.x": ["IVF_FLAT", "IVF_SQ8", "IVF_PQ"],
            "2.0.x": ["IVF_FLAT", "IVF_SQ8"],
            "default": ["IVF_FLAT", "IVF_SQ8", "IVF_PQ", "HNSW"]
        }
    }
    
    def __init__(self):
        self.pymilvus_version = pymilvus_version
        self.server_version = None
        self._detect_versions()
    
    def _detect_versions(self):
        """检测Milvus版本"""
        try:
            if PYMILVUS_AVAILABLE:
                logger.info(f"PyMilvus客户端版本: {self.pymilvus_version}")

                if utility:
                    try:
                        self.server_version = utility.get_server_version()
                        logger.info(f"Milvus服务器版本: {self.server_version}")
                    except Exception as e:
                        logger.debug(f"无法获取服务器版本: {e}")
            else:
                logger.warning("PyMilvus未安装，使用默认兼容性配置")

        except Exception as e:
            logger.debug(f"版本检测失败: {e}")
    
    def get_version_key(self, version: str) -> str:
        """获取版本键"""
        if not version:
            return "default"
        
        # 提取主版本号
        match = re.match(r'(\d+\.\d+)', version)
        if match:
            major_minor = match.group(1)
            return f"{major_minor}.x"
        
        return "default"
    
    def get_compatible_scalar_index_types(self) -> List[str]:
        """获取兼容的标量索引类型"""
        version_key = self.get_version_key(self.server_version or self.pymilvus_version)
        
        scalar_types = self.INDEX_TYPE_COMPATIBILITY["scalar"]
        
        if version_key in scalar_types:
            return scalar_types[version_key]
        
        return scalar_types["default"]
    
    def get_compatible_vector_index_types(self) -> List[str]:
        """获取兼容的向量索引类型"""
        version_key = self.get_version_key(self.server_version or self.pymilvus_version)
        
        vector_types = self.INDEX_TYPE_COMPATIBILITY["vector"]
        
        if version_key in vector_types:
            return vector_types[version_key]
        
        return vector_types["default"]
    
    def is_scalar_index_type_supported(self, index_type: str) -> bool:
        """检查标量索引类型是否支持"""
        supported_types = self.get_compatible_scalar_index_types()
        return index_type in supported_types
    
    def is_vector_index_type_supported(self, index_type: str) -> bool:
        """检查向量索引类型是否支持"""
        supported_types = self.get_compatible_vector_index_types()
        return index_type in supported_types
    
    def get_recommended_scalar_index_type(self) -> str:
        """获取推荐的标量索引类型"""
        compatible_types = self.get_compatible_scalar_index_types()
        return compatible_types[0] if compatible_types else "Trie"
    
    def get_recommended_vector_index_type(self) -> str:
        """获取推荐的向量索引类型"""
        compatible_types = self.get_compatible_vector_index_types()
        return compatible_types[0] if compatible_types else "IVF_FLAT"
    
    def create_scalar_index_with_fallback(self, collection, field_name: str) -> bool:
        """使用回退机制创建标量索引"""
        compatible_types = self.get_compatible_scalar_index_types()
        
        for index_type in compatible_types:
            try:
                logger.debug(f"尝试为字段 {field_name} 创建 {index_type} 索引")
                collection.create_index(
                    field_name=field_name,
                    index_params={"index_type": index_type}
                )
                logger.info(f"成功为字段 {field_name} 创建 {index_type} 索引")
                return True
                
            except Exception as e:
                logger.debug(f"索引类型 {index_type} 失败: {e}")
                continue
        
        logger.warning(f"无法为字段 {field_name} 创建任何索引类型")
        return False
    
    def create_vector_index_with_fallback(self, collection, field_name: str, 
                                        metric_type: str = "IP", 
                                        params: Optional[Dict] = None) -> bool:
        """使用回退机制创建向量索引"""
        compatible_types = self.get_compatible_vector_index_types()
        
        if params is None:
            params = {"nlist": 1024}
        
        for index_type in compatible_types:
            try:
                index_params = {
                    "metric_type": metric_type,
                    "index_type": index_type,
                    "params": params
                }
                
                logger.debug(f"尝试为字段 {field_name} 创建 {index_type} 向量索引")
                collection.create_index(
                    field_name=field_name,
                    index_params=index_params
                )
                logger.info(f"成功为字段 {field_name} 创建 {index_type} 向量索引")
                return True
                
            except Exception as e:
                logger.debug(f"向量索引类型 {index_type} 失败: {e}")
                continue
        
        logger.warning(f"无法为字段 {field_name} 创建任何向量索引类型")
        return False
    
    def diagnose_index_error(self, error_message: str) -> Dict[str, str]:
        """诊断索引错误并提供建议"""
        diagnosis = {
            "error_type": "unknown",
            "suggested_fix": "检查Milvus版本兼容性",
            "recommended_index_type": None
        }
        
        error_lower = error_message.lower()
        
        # 检查索引类型错误
        if "index type not match" in error_lower or "invalid parameter" in error_lower:
            diagnosis["error_type"] = "index_type_mismatch"
            
            if "trie" in error_lower:
                diagnosis["suggested_fix"] = "使用兼容的标量索引类型"
                diagnosis["recommended_index_type"] = self.get_recommended_scalar_index_type()
            else:
                diagnosis["suggested_fix"] = "使用兼容的向量索引类型"
                diagnosis["recommended_index_type"] = self.get_recommended_vector_index_type()
        
        # 检查参数错误
        elif "parameter" in error_lower and "expected" in error_lower:
            diagnosis["error_type"] = "parameter_mismatch"
            diagnosis["suggested_fix"] = "检查索引参数格式和值"
        
        # 检查连接错误
        elif "connection" in error_lower or "connect" in error_lower:
            diagnosis["error_type"] = "connection_error"
            diagnosis["suggested_fix"] = "检查Milvus服务器连接"
        
        return diagnosis
    
    def get_compatibility_report(self) -> Dict[str, any]:
        """获取兼容性报告"""
        return {
            "pymilvus_version": self.pymilvus_version,
            "server_version": self.server_version,
            "supported_scalar_index_types": self.get_compatible_scalar_index_types(),
            "supported_vector_index_types": self.get_compatible_vector_index_types(),
            "recommended_scalar_index": self.get_recommended_scalar_index_type(),
            "recommended_vector_index": self.get_recommended_vector_index_type()
        }


# 全局兼容性管理器实例
_compatibility_manager = None


def get_compatibility_manager() -> MilvusCompatibilityManager:
    """获取兼容性管理器实例"""
    global _compatibility_manager
    if _compatibility_manager is None:
        _compatibility_manager = MilvusCompatibilityManager()
    return _compatibility_manager


def create_index_with_compatibility(collection, field_name: str, 
                                  field_type: str = "scalar", 
                                  **kwargs) -> bool:
    """使用兼容性检查创建索引"""
    manager = get_compatibility_manager()
    
    if field_type == "scalar":
        return manager.create_scalar_index_with_fallback(collection, field_name)
    elif field_type == "vector":
        return manager.create_vector_index_with_fallback(
            collection, field_name, 
            kwargs.get("metric_type", "IP"),
            kwargs.get("params", {"nlist": 1024})
        )
    else:
        logger.error(f"不支持的字段类型: {field_type}")
        return False


def diagnose_milvus_error(error_message: str) -> Dict[str, str]:
    """诊断Milvus错误"""
    manager = get_compatibility_manager()
    return manager.diagnose_index_error(error_message)


def print_compatibility_report():
    """打印兼容性报告"""
    manager = get_compatibility_manager()
    report = manager.get_compatibility_report()
    
    print("\n" + "="*50)
    print("Milvus兼容性报告")
    print("="*50)
    print(f"PyMilvus版本: {report['pymilvus_version']}")
    print(f"服务器版本: {report['server_version'] or '未知'}")
    print(f"支持的标量索引: {', '.join(report['supported_scalar_index_types'])}")
    print(f"支持的向量索引: {', '.join(report['supported_vector_index_types'])}")
    print(f"推荐标量索引: {report['recommended_scalar_index']}")
    print(f"推荐向量索引: {report['recommended_vector_index']}")
    print("="*50)


if __name__ == "__main__":
    # 测试兼容性管理器
    print_compatibility_report()
    
    # 测试错误诊断
    test_error = "MilvusException: (code=1100, message=index type not match: invalid parameter[expected=Trie][actual=TRIE])"
    diagnosis = diagnose_milvus_error(test_error)
    
    print(f"\n错误诊断:")
    print(f"错误类型: {diagnosis['error_type']}")
    print(f"建议修复: {diagnosis['suggested_fix']}")
    print(f"推荐索引: {diagnosis['recommended_index_type']}")
