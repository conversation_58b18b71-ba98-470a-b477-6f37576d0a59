2025-06-27 14:04:00 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-06-27 14:04:00 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-06-27 14:04:00 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-06-27 14:04:00 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-06-27 14:04:00 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-06-27 14:04:00 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-06-27 14:04:00 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-06-27 14:04:00 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-06-27 14:04:00 | INFO     | app.main:validate_system_config:139 | 验证系统配置...
2025-06-27 14:04:00 | INFO     | app.core.llms:get_model_config_status:100 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-06-27 14:04:00 | INFO     | app.main:validate_system_config:148 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-06-27 14:04:00 | INFO     | app.main:validate_system_config:151 | ✅ 多模态服务验证完成
2025-06-27 14:04:00 | INFO     | app.main:init_databases:161 | 初始化数据库连接...
2025-06-27 14:04:00 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-06-27 14:04:00 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: localhost:3306/ai_automation
2025-06-27 14:04:00 | INFO     | app.database.connection:initialize:58 | 数据库连接初始化成功
2025-06-27 14:04:01 | INFO     | app.database.connection:create_tables:74 | 数据库表创建成功
2025-06-27 14:04:01 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-06-27 14:04:01 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-06-27 14:04:01 | INFO     | app.main:init_databases:168 | ✅ 主数据库连接初始化完成
2025-06-27 14:04:01 | INFO     | app.main:init_databases:176 | ✅ 数据库连接初始化完成
2025-06-27 14:04:01 | INFO     | app.main:warmup_ai_models:187 | 预热AI模型...
2025-06-27 14:04:01 | INFO     | app.main:warmup_ai_models:198 | ✅ AI模型预热完成
2025-06-27 14:04:01 | INFO     | app.main:init_rag_system:213 | 初始化RAG系统...
2025-06-27 14:04:01 | WARNING  | app.rag.document_processor:<module>:23 | PyPDF2未安装，无法处理PDF文件。安装命令: pip install PyPDF2
2025-06-27 14:04:01 | WARNING  | app.rag.document_processor:<module>:30 | python-docx未安装，无法处理DOCX文件。安装命令: pip install python-docx
2025-06-27 14:04:43 | INFO     | app.rag.rag_system:initialize:90 | 正在初始化RAG系统...
2025-06-27 14:04:44 | WARNING  | app.rag.embeddings:_check_model_availability:67 | 模型 nomic-embed-text 不在可用模型列表中: ['nomic-embed-text:latest', 'sciphi/triplex:latest']
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:_pull_model:82 | 正在拉取模型: nomic-embed-text
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:_pull_model:95 | 拉取进度: pulling manifest
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:_pull_model:95 | 拉取进度: pulling 970aa74c0a90
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:_pull_model:95 | 拉取进度: pulling c71d239df917
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:_pull_model:95 | 拉取进度: pulling ce4a164fc046
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:_pull_model:95 | 拉取进度: pulling 31df23ea7daa
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:_pull_model:95 | 拉取进度: verifying sha256 digest
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:_pull_model:95 | 拉取进度: writing manifest
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:_pull_model:95 | 拉取进度: success
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:_pull_model:97 | 模型拉取完成
2025-06-27 14:04:44 | INFO     | app.rag.embeddings:initialize:48 | 嵌入服务初始化成功，模型: nomic-embed-text
2025-06-27 14:04:54 | ERROR    | app.rag.vector_store:connect:58 | 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on 127.0.0.1:19530, illegal connection params or server unavailable)>
2025-06-27 14:04:54 | ERROR    | app.rag.rag_system:initialize:109 | RAG系统初始化失败: <MilvusException: (code=2, message=Fail connecting to server on 127.0.0.1:19530, illegal connection params or server unavailable)>
2025-06-27 14:04:54 | ERROR    | app.main:init_rag_system:229 | RAG系统初始化失败: <MilvusException: (code=2, message=Fail connecting to server on 127.0.0.1:19530, illegal connection params or server unavailable)>
2025-06-27 14:04:54 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-06-27 14:06:06 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-06-27 14:06:06 | INFO     | app.rag.rag_system:cleanup:124 | RAG系统资源清理完成
2025-06-27 14:06:06 | INFO     | app.main:cleanup_resources:302 | ✅ RAG系统清理完成
2025-06-27 14:06:06 | INFO     | app.core.database_startup:cleanup_database_on_shutdown:54 | 🔄 正在关闭数据库连接...
2025-06-27 14:06:06 | INFO     | app.database.connection:close:115 | 数据库连接已关闭
2025-06-27 14:06:06 | INFO     | app.core.database_startup:cleanup_database_on_shutdown:56 | ✅ 数据库连接已关闭
2025-06-27 14:06:06 | INFO     | app.main:cleanup_resources:317 | ✅ 资源清理完成
2025-06-27 14:06:06 | INFO     | app.main:lifespan:57 | ✅ 系统关闭完成
