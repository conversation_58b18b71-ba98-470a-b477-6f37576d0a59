2025-06-24 18:29:04 | ERROR    | app.api.v1.endpoints.web.script_management:save_script_from_session:409 | 从会话保存脚本失败: DatabaseScriptService.create_script_from_analysis() missing 1 required positional argument: 'file_path'
2025-06-24 18:29:04 | ERROR    | app.api.v1.endpoints.web.script_management:save_script_from_session:409 | 从会话保存脚本失败: DatabaseScriptService.create_script_from_analysis() missing 1 required positional argument: 'file_path'
2025-06-24 18:31:32 | ERROR    | app.api.v1.endpoints.web.script_management:save_script_from_session:409 | 从会话保存脚本失败: DatabaseScriptService.create_script_from_analysis() missing 1 required positional argument: 'file_path'
2025-06-24 18:31:32 | ERROR    | app.api.v1.endpoints.web.script_management:save_script_from_session:409 | 从会话保存脚本失败: DatabaseScriptService.create_script_from_analysis() missing 1 required positional argument: 'file_path'
2025-06-24 19:31:12 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:421 | 从会话保存脚本成功: c4ac7321-3df8-44c8-8e4e-feaaaf6fdb50 - AI生成YAML脚本_2025/6/24
2025-06-24 19:31:12 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:421 | 从会话保存脚本成功: 67fa2546-8d50-40b7-a5ae-a8f2ba9a70b8 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 19:31:12 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:421 | 从会话保存脚本成功: 3d8fc0d3-46c4-491f-8906-858b07df0ecd - AI生成YAML脚本_2025/6/24
2025-06-24 19:31:12 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:421 | 从会话保存脚本成功: 3414a39b-a999-450e-af68-2860b624ee43 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 19:43:33 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:421 | 从会话保存脚本成功: 4b19b73a-94e3-4d22-8b21-f8a2e75de32d - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 19:43:34 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:421 | 从会话保存脚本成功: f7a8076b-6bf8-4ab3-9aee-07c26a9719f5 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 19:45:42 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:181 | 脚本删除成功: ffd1ee35-f5c2-4e18-aaca-956f630672b6
2025-06-24 19:45:53 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:181 | 脚本删除成功: 3414a39b-a999-450e-af68-2860b624ee43
2025-06-24 19:45:56 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:181 | 脚本删除成功: 3d8fc0d3-46c4-491f-8906-858b07df0ecd
2025-06-24 19:45:59 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:181 | 脚本删除成功: 67fa2546-8d50-40b7-a5ae-a8f2ba9a70b8
2025-06-24 19:46:01 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:181 | 脚本删除成功: c4ac7321-3df8-44c8-8e4e-feaaaf6fdb50
2025-06-24 19:58:32 | INFO     | app.api.v1.endpoints.web.script_management:create_script:97 | 脚本创建成功: 8bd1e369-9bef-4e54-83b1-4e341b4b6934 - 测试执行脚本
2025-06-24 19:58:32 | INFO     | app.api.v1.endpoints.web.script_management:create_script:97 | 脚本创建成功: 00797877-03d1-4b77-ab02-91572dc221e5 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 20:03:39 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:181 | 脚本删除成功: 00797877-03d1-4b77-ab02-91572dc221e5
2025-06-24 20:03:41 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:181 | 脚本删除成功: 8bd1e369-9bef-4e54-83b1-4e341b4b6934
2025-06-24 20:03:44 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:181 | 脚本删除成功: 4b19b73a-94e3-4d22-8b21-f8a2e75de32d
2025-06-24 20:03:46 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:181 | 脚本删除成功: f7a8076b-6bf8-4ab3-9aee-07c26a9719f5
2025-06-24 20:05:23 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:421 | 从会话保存脚本成功: 9eedcc02-e841-4849-abb7-857526ad95ff - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 20:05:23 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:421 | 从会话保存脚本成功: 1f1b53ec-5329-417f-9670-0a2592d0aa05 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 20:17:29 | INFO     | app.api.v1.endpoints.web.script_management:create_script:97 | 脚本创建成功: a0bdeda6-53c8-4d6d-98e2-1000e8420634 - 测试执行脚本
2025-06-24 20:17:29 | INFO     | app.api.v1.endpoints.web.script_management:create_script:97 | 脚本创建成功: 09e0bc61-a9d4-4176-a4fb-7cbb54e104fd - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 20:17:29 | INFO     | app.api.v1.endpoints.web.script_management:create_script:97 | 脚本创建成功: de6da5ef-20f1-4ef9-803a-31fb91847a4c - 测试自动修复脚本
2025-06-24 20:51:33 | INFO     | app.api.v1.endpoints.web.script_management:create_script:109 | 脚本创建成功: dfb50cf2-34a3-4d59-ad56-9af5717a1979 - 工作空间测试脚本
2025-06-24 21:17:52 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: dfb50cf2-34a3-4d59-ad56-9af5717a1979
2025-06-24 21:17:53 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: 09e0bc61-a9d4-4176-a4fb-7cbb54e104fd
2025-06-24 21:17:56 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: a0bdeda6-53c8-4d6d-98e2-1000e8420634
2025-06-24 21:17:58 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: de6da5ef-20f1-4ef9-803a-31fb91847a4c
2025-06-24 21:18:01 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: 1f1b53ec-5329-417f-9670-0a2592d0aa05
2025-06-24 21:18:03 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: 9eedcc02-e841-4849-abb7-857526ad95ff
2025-06-24 21:19:13 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:450 | 从会话保存脚本成功: 6fc58821-3b15-4e7c-bbe4-28ed9ebc9491 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 21:19:13 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:450 | 从会话保存脚本成功: 05628bd2-21f2-4e6f-9e0b-2b2a3409a133 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 21:20:12 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:450 | 从会话保存脚本成功: 4e841969-7134-4925-b01f-10500d6c1377 - 登录共同照护web
2025-06-24 22:04:24 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: 4e841969-7134-4925-b01f-10500d6c1377
2025-06-24 22:04:27 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: 05628bd2-21f2-4e6f-9e0b-2b2a3409a133
2025-06-24 22:04:29 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: 6fc58821-3b15-4e7c-bbe4-28ed9ebc9491
2025-06-24 22:05:23 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:450 | 从会话保存脚本成功: 4c720440-8bac-4257-9efd-ea99db1e8146 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 22:05:24 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:450 | 从会话保存脚本成功: ea920140-3125-486b-8b56-ca40aae07114 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 22:06:42 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_id:145 | 返回脚本 4c720440-8bac-4257-9efd-ea99db1e8146 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-06-08-b1msi7qo.html
2025-06-24 22:38:16 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: 4c720440-8bac-4257-9efd-ea99db1e8146
2025-06-24 22:38:19 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: ea920140-3125-486b-8b56-ca40aae07114
2025-06-24 22:39:11 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:450 | 从会话保存脚本成功: 87f13659-4e52-4f51-ab02-f94cebbb9a59 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 22:39:11 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:450 | 从会话保存脚本成功: 342839a4-d3b1-44aa-8222-1684679bcac7 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 22:39:59 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_id:145 | 返回脚本 342839a4-d3b1-44aa-8222-1684679bcac7 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-39-43-t78wkkck.html
2025-06-24 22:41:23 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_id:145 | 返回脚本 342839a4-d3b1-44aa-8222-1684679bcac7 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-39-43-t78wkkck.html
2025-06-24 22:44:04 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_id:145 | 返回脚本 342839a4-d3b1-44aa-8222-1684679bcac7 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-39-43-t78wkkck.html
2025-06-24 22:44:57 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_id:145 | 返回脚本 87f13659-4e52-4f51-ab02-f94cebbb9a59 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-44-46-0tro07xt.html
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '342839a4-d3b1-44aa-8222-1684679bcac7' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '87f13659-4e52-4f51-ab02-f94cebbb9a59' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=10, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=9, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '342839a4-d3b1-44aa-8222-1684679bcac7' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '87f13659-4e52-4f51-ab02-f94cebbb9a59' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=10, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '342839a4-d3b1-44aa-8222-1684679bcac7' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=9, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '87f13659-4e52-4f51-ab02-f94cebbb9a59' 的最新测试报告
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=10, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=9, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:13 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '342839a4-d3b1-44aa-8222-1684679bcac7' 的最新测试报告
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '87f13659-4e52-4f51-ab02-f94cebbb9a59' 的最新测试报告
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=10, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=9, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:18 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '342839a4-d3b1-44aa-8222-1684679bcac7' 的最新测试报告
2025-06-24 23:14:18 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=10, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:14:18 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 '342839a4-d3b1-44aa-8222-1684679bcac7' 的测试报告
2025-06-24 23:14:18 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=10, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts, 执行ID=97f2c1ac-ff18-4b3b-9147-f6acec3c0a5e
2025-06-24 23:14:18 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 '342839a4-d3b1-44aa-8222-1684679bcac7' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-24 23:14:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:14:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:35 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 '登录共同照护web_20250624_212036.spec.ts' 的测试报告
2025-06-24 23:14:35 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:152 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:14:35 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts, 执行ID=1e5d1382-1139-4516-af12-7316a7f0c4cd
2025-06-24 23:14:35 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 '登录共同照护web_20250624_212036.spec.ts' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-24 23:33:05 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: 342839a4-d3b1-44aa-8222-1684679bcac7
2025-06-24 23:33:05 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '87f13659-4e52-4f51-ab02-f94cebbb9a59' 的最新测试报告
2025-06-24 23:33:05 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:33:05 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:33:06 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=9, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:33:06 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:33:06 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:06 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:07 | INFO     | app.api.v1.endpoints.web.script_management:delete_script:193 | 脚本删除成功: 87f13659-4e52-4f51-ab02-f94cebbb9a59
2025-06-24 23:33:08 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:33:08 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:33:08 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:33:08 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:08 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:33:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:34:31 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:450 | 从会话保存脚本成功: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 23:34:31 | INFO     | app.api.v1.endpoints.web.script_management:save_script_from_session:450 | 从会话保存脚本成功: c9d70933-19d6-4d4d-b91b-f44d18565508 - AI生成PLAYWRIGHT脚本_2025/6/24
2025-06-24 23:34:36 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-24 23:34:36 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-24 23:34:36 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:34:36 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:34:36 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-24 23:34:36 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-24 23:34:36 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:34:36 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:34:36 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-24 23:34:36 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-24 23:34:36 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:34:37 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-24 23:34:37 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:34:37 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:34:37 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:34:37 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-24 23:34:37 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-24 23:34:37 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-24 23:35:19 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-24 23:35:19 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:35:19 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-24 23:35:19 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:35:19 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-24 23:35:19 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:35:19 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-24 23:35:19 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:36:03 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-24 23:36:03 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:36:03 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-24 23:36:03 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts, 执行ID=e555e360-36d8-4408-a133-c8933ef8876d
2025-06-24 23:36:03 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 00:05:52 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 00:05:52 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 00:05:52 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的测试报告
2025-06-25 00:05:52 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '登录共同照护web_20250624_212036.spec.ts' 的测试报告
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 00:05:52 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的测试报告
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '登录共同照护web_20250624_212036.spec.ts' 的测试报告
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的测试报告
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '登录共同照护web_20250624_212036.spec.ts' 的测试报告
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-25 00:05:53 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的测试报告
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 00:05:53 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '登录共同照护web_20250624_212036.spec.ts' 的测试报告
2025-06-25 00:06:07 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 00:06:07 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-25 00:06:07 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 00:06:07 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 00:06:07 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-25 00:06:07 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 00:06:07 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 00:06:07 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-25 00:06:07 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 00:06:07 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 00:06:07 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: c9d70933-19d6-4d4d-b91b-f44d18565508
2025-06-25 00:06:07 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:26:41 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:26:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:26:42 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:26:42 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:26:44 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts, 执行ID=e555e360-36d8-4408-a133-c8933ef8876d
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:26:44 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:26:44 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-25 08:28:31 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:28:31 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:28:31 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:28:31 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:28:31 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:31 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:28:31 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:31 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:28:31 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:28:31 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:28:32 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:32 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:28:32 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:28:32 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:28:32 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:28:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:28:44 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:44 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 08:28:44 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts, 执行ID=e555e360-36d8-4408-a133-c8933ef8876d
2025-06-25 08:28:44 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-25 08:28:55 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:28:55 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:28:55 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:28:55 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:55 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:28:55 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:28:55 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:28:55 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:28:55 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:28:55 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:29:09 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:29:09 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:29:09 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:29:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:29:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:29:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:29:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:29:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:29:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:29:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:29:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:29:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:29:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:29:11 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:29:11 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:29:11 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:29:11 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:29:11 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:29:11 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:29:11 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:29:11 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:29:11 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:29:11 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:30:04 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:30:04 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:30:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:04 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:57:04 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:04 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:04 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:57:04 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:57:26 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:26 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:26 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:26 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 08:57:26 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 08:57:29 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:29 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:29 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 08:57:29 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=13, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts, 执行ID=e555e360-36d8-4408-a133-c8933ef8876d
2025-06-25 08:57:29 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:57:50 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:59:48 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 08:59:48 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:59:48 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 08:59:48 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts, 执行ID=5a917138-6885-4e0f-8806-6b8824bf3da8
2025-06-25 08:59:48 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:01:49 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:01:49 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:01:49 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:01:49 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:01:49 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:01:54 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:01:54 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:01:54 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 09:01:54 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts, 执行ID=5a917138-6885-4e0f-8806-6b8824bf3da8
2025-06-25 09:01:54 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:02:33 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:02:33 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:02:33 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:02:33 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:02:33 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:02:43 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:02:43 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:02:43 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 09:02:43 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts, 执行ID=5a917138-6885-4e0f-8806-6b8824bf3da8
2025-06-25 09:02:43 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:10 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:14:10 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:10 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:10 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:14:10 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:15 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:15 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:14:15 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:15 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:14:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:14:16 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:14:16 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:14:16 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告
2025-06-25 09:14:16 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts, 执行ID=5a917138-6885-4e0f-8806-6b8824bf3da8
2025-06-25 09:14:16 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:17:14 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:17:14 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:17:14 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'c9d70933-19d6-4d4d-b91b-f44d18565508' 的最新测试报告
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 'AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts' 的最新测试报告
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=16, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=12, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:15 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '登录共同照护web_20250624_212036.spec.ts' 的最新测试报告
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:32 | 按脚本ID未找到，尝试按脚本名称查找: 8cde341b-5a51-4153-b5f9-f2788cd7d4e1
2025-06-25 09:17:15 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=3, 脚本名称=登录共同照护web_20250624_212036.spec.ts
2025-06-25 09:17:15 | WARNING  | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:36 | 未找到脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:17:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:17:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=18, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:17:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=18, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:17:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=18, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:17:35 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=18, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:25 | API请求: 获取脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的最新测试报告
2025-06-25 09:17:41 | INFO     | app.api.v1.endpoints.web.test_reports:get_latest_report_by_script_identifier:39 | 成功获取报告: ID=18, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:41 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:145 | 查找脚本标识符 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告
2025-06-25 09:17:41 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:159 | 找到报告: ID=18, 脚本名称=AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts, 执行ID=533eddb3-6599-4027-acaf-1842237ffaca
2025-06-25 09:17:41 | INFO     | app.api.v1.endpoints.web.test_reports:view_latest_report_by_script_identifier:171 | 返回脚本 '8cde341b-5a51-4153-b5f9-f2788cd7d4e1' 的测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-25 10:11:14 | INFO     | app.api.v1.endpoints.web.test_reports:view_report_html:110 | 返回测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
2025-06-25 10:19:32 | INFO     | app.api.v1.endpoints.web.test_reports:view_report_html:110 | 返回测试报告文件: /Users/<USER>/workspace/playwright-workspace/playwright-report/index.html
