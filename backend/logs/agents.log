2025-06-24 20:19:51 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 20:19:51 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 20:19:51 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 20:19:51 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 20:19:51 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 20:19:51 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 20:19:51 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 20:19:51 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 20:19:51 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 20:19:51 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 20:19:51 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 20:19:51 | INFO     | app.agents.web.playwright_executor:__init__:43 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 20:19:51 | INFO     | app.agents.web.playwright_executor:__init__:44 | 执行环境路径: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:19:51 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 20:19:51 | ERROR    | app.agents.web.playwright_executor:_validate_workspace:56 | package.json不存在: C:\Users\<USER>\Desktop\workspace\playwright-workspace/package.json
2025-06-24 20:19:51 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: Playwright工作空间验证失败
2025-06-24 20:19:51 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 20:22:14 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 20:22:14 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 20:22:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 20:22:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 20:22:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 20:22:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 20:22:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 20:22:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 20:22:14 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 20:22:14 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 20:22:14 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 20:22:14 | INFO     | app.agents.web.playwright_executor:__init__:43 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 20:22:14 | INFO     | app.agents.web.playwright_executor:__init__:44 | 执行环境路径: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:22:14 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 20:22:14 | ERROR    | app.agents.web.playwright_executor:_validate_workspace:56 | package.json不存在: C:\Users\<USER>\Desktop\workspace\playwright-workspace/package.json
2025-06-24 20:22:14 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: Playwright工作空间验证失败
2025-06-24 20:22:14 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 20:39:47 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 20:39:47 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 20:39:47 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 20:39:48 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 20:39:48 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 20:39:48 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 20:39:48 | INFO     | app.agents.web.playwright_executor:__init__:43 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 20:39:48 | INFO     | app.agents.web.playwright_executor:__init__:44 | 执行环境路径: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:39:48 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 20:39:48 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 20:39:48 | INFO     | app.agents.web.playwright_executor:__init__:43 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 20:39:48 | INFO     | app.agents.web.playwright_executor:__init__:44 | 执行环境路径: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:39:48 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 20:39:48 | ERROR    | app.agents.web.playwright_executor:_validate_workspace:56 | package.json不存在: C:\Users\<USER>\Desktop\workspace\playwright-workspace/package.json
2025-06-24 20:39:48 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: Playwright工作空间验证失败
2025-06-24 20:39:48 | ERROR    | app.agents.web.playwright_executor:_validate_workspace:56 | package.json不存在: C:\Users\<USER>\Desktop\workspace\playwright-workspace/package.json
2025-06-24 20:39:48 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: Playwright工作空间验证失败
2025-06-24 20:39:48 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 20:39:48 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 20:41:03 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 20:41:03 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 20:41:03 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 20:41:03 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 20:41:03 | INFO     | app.agents.web.playwright_executor:__init__:43 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 20:41:03 | INFO     | app.agents.web.playwright_executor:__init__:44 | 执行环境路径: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:41:03 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 20:41:03 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 20:41:03 | INFO     | app.agents.web.playwright_executor:__init__:43 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 20:41:03 | INFO     | app.agents.web.playwright_executor:__init__:44 | 执行环境路径: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:41:03 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 20:41:03 | ERROR    | app.agents.web.playwright_executor:_validate_workspace:56 | package.json不存在: C:\Users\<USER>\Desktop\workspace\playwright-workspace/package.json
2025-06-24 20:41:03 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: Playwright工作空间验证失败
2025-06-24 20:41:03 | ERROR    | app.agents.web.playwright_executor:_validate_workspace:56 | package.json不存在: C:\Users\<USER>\Desktop\workspace\playwright-workspace/package.json
2025-06-24 20:41:03 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: Playwright工作空间验证失败
2025-06-24 20:41:03 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 20:41:03 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 20:52:56 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 20:52:56 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 20:52:56 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 20:52:56 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 20:52:56 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 20:52:56 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 20:52:56 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 20:52:56 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 20:52:56 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 20:52:56 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 20:52:56 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 20:52:56 | INFO     | app.agents.web.playwright_executor:__init__:43 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 20:52:56 | INFO     | app.agents.web.playwright_executor:__init__:44 | 执行环境路径: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:52:56 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 20:52:56 | ERROR    | app.agents.web.playwright_executor:_validate_workspace:56 | package.json不存在: C:\Users\<USER>\Desktop\workspace\playwright-workspace/package.json
2025-06-24 20:52:56 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: Playwright工作空间验证失败
2025-06-24 20:52:56 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 20:57:42 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 20:57:42 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 20:57:42 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 20:57:42 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 20:57:42 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 20:57:42 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 20:57:42 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 20:57:42 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 20:57:42 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 20:57:42 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 20:57:42 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 20:57:42 | INFO     | app.agents.web.playwright_executor:__init__:43 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 20:57:42 | INFO     | app.agents.web.playwright_executor:__init__:44 | 执行环境路径: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:57:42 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 20:57:42 | INFO     | app.agents.web.playwright_executor:handle_execution_request:421 | 工作空间健康检查结果: critical
2025-06-24 20:57:42 | WARNING  | app.agents.web.playwright_executor:handle_execution_request:425 | 工作空间存在严重问题，尝试自动修复...
2025-06-24 20:57:42 | INFO     | app.agents.web.playwright_executor:_create_package_json:143 | 创建package.json文件: C:\Users\<USER>\Desktop\workspace\playwright-workspace/package.json
2025-06-24 20:57:42 | INFO     | app.agents.web.playwright_executor:_create_playwright_config:218 | 创建playwright.config.ts文件: C:\Users\<USER>\Desktop\workspace\playwright-workspace/playwright.config.ts
2025-06-24 20:57:42 | ERROR    | app.agents.web.playwright_executor:handle_execution_request:434 | 工作空间自动修复失败: node_modules目录不存在
2025-06-24 20:57:42 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: 工作空间自动修复失败: node_modules目录不存在
2025-06-24 20:57:42 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: 建议手动执行以下操作:
运行: cd C:\Users\<USER>\Desktop\workspace\playwright-workspace && npm install
2025-06-24 20:57:42 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 20:58:16 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 20:58:16 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 20:58:16 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 20:58:16 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:__init__:43 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:__init__:44 | 执行环境路径: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:58:16 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 20:58:16 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:__init__:43 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:__init__:44 | 执行环境路径: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:58:16 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:handle_execution_request:421 | 工作空间健康检查结果: warning
2025-06-24 20:58:16 | WARNING  | app.agents.web.playwright_executor:_validate_workspace:81 | node_modules不存在，可能需要运行npm install: C:\Users\<USER>\Desktop\workspace\playwright-workspace/node_modules
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:_validate_workspace:83 | Playwright工作空间验证通过
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:402 | 找到现有脚本文件: C:\Users\<USER>\Desktop\workspace\playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_201950.spec.ts
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:512 | 使用现有脚本文件: C:\Users\<USER>\Desktop\workspace\playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_201950.spec.ts
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:641 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_201950.spec.ts
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:642 | 工作目录: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:handle_execution_request:421 | 工作空间健康检查结果: warning
2025-06-24 20:58:16 | WARNING  | app.agents.web.playwright_executor:_validate_workspace:81 | node_modules不存在，可能需要运行npm install: C:\Users\<USER>\Desktop\workspace\playwright-workspace/node_modules
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:_validate_workspace:83 | Playwright工作空间验证通过
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:402 | 找到现有脚本文件: C:\Users\<USER>\Desktop\workspace\playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_201950.spec.ts
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:512 | 使用现有脚本文件: C:\Users\<USER>\Desktop\workspace\playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_201950.spec.ts
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:641 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_201950.spec.ts
2025-06-24 20:58:16 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:642 | 工作目录: C:\Users\<USER>\Desktop\workspace\playwright-workspace
2025-06-24 20:58:20 | INFO     | app.agents.web.playwright_executor:read_stdout:750 | [Playwright] Need to install the following packages:
2025-06-24 20:58:20 | INFO     | app.agents.web.playwright_executor:read_stdout:750 | [Playwright] playwright@1.53.1
2025-06-24 20:58:20 | INFO     | app.agents.web.playwright_executor:read_stdout:750 | [Playwright] Need to install the following packages:
2025-06-24 20:58:20 | INFO     | app.agents.web.playwright_executor:read_stdout:750 | [Playwright] playwright@1.53.1
2025-06-24 21:03:20 | INFO     | app.agents.web.playwright_executor:read_stdout:750 | [Playwright] Ok to proceed? (y)
2025-06-24 21:03:20 | INFO     | app.agents.web.playwright_executor:read_stdout:750 | [Playwright] Ok to proceed? (y)
2025-06-24 21:03:20 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1177 | 测试报告已保存到数据库: 1
2025-06-24 21:03:20 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 303.49秒
2025-06-24 21:03:20 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1177 | 测试报告已保存到数据库: 2
2025-06-24 21:03:20 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 303.51秒
2025-06-24 21:03:20 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 21:03:20 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 21:20:36 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 21:20:36 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 21:20:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 21:20:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 21:20:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 21:20:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 21:20:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 21:20:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 21:20:36 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 21:20:36 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 21:20:36 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 21:20:36 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 21:20:36 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 21:20:36 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 21:20:36 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 21:20:36 | INFO     | app.agents.web.playwright_executor:handle_execution_request:424 | 工作空间健康检查结果: critical
2025-06-24 21:20:36 | WARNING  | app.agents.web.playwright_executor:handle_execution_request:428 | 工作空间存在严重问题，尝试自动修复...
2025-06-24 21:20:36 | INFO     | app.agents.web.playwright_executor:_create_package_json:146 | 创建package.json文件: /Users/<USER>/workspace/playwright-workspace/package.json
2025-06-24 21:20:36 | INFO     | app.agents.web.playwright_executor:_create_playwright_config:221 | 创建playwright.config.ts文件: /Users/<USER>/workspace/playwright-workspace/playwright.config.ts
2025-06-24 21:20:36 | ERROR    | app.agents.web.playwright_executor:handle_execution_request:437 | 工作空间自动修复失败: node_modules目录不存在
2025-06-24 21:20:36 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: 工作空间自动修复失败: node_modules目录不存在
2025-06-24 21:20:36 | ERROR    | app.core.agents.base:send_error:109 | [Playwright执行智能体] 错误: 建议手动执行以下操作:
运行: cd /Users/<USER>/workspace/playwright-workspace && npm install
2025-06-24 21:20:36 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 21:34:14 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 21:34:14 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 21:34:14 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 21:34:14 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 21:34:14 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 21:34:14 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 21:34:14 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 21:34:14 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 21:34:14 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 21:34:14 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 21:34:14 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 21:34:14 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 21:34:14 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 21:34:15 | WARNING  | app.agents.web.playwright_executor:_validate_workspace:84 | node_modules不存在，可能需要运行npm install: /Users/<USER>/workspace/playwright-workspace/node_modules
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/登录共同照护web_20250624_212036.spec.ts
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/登录共同照护web_20250624_212036.spec.ts
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/登录共同照护web_20250624_212036.spec.ts
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 21:34:15 | WARNING  | app.agents.web.playwright_executor:_validate_workspace:84 | node_modules不存在，可能需要运行npm install: /Users/<USER>/workspace/playwright-workspace/node_modules
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/登录共同照护web_20250624_212036.spec.ts
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/登录共同照护web_20250624_212036.spec.ts
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/登录共同照护web_20250624_212036.spec.ts
2025-06-24 21:34:15 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 21:34:17 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Need to install the following packages:
2025-06-24 21:34:17 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] playwright@1.53.1
2025-06-24 21:34:17 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Need to install the following packages:
2025-06-24 21:34:17 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] playwright@1.53.1
2025-06-24 21:40:55 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Ok to proceed? (y)
2025-06-24 21:40:55 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Ok to proceed? (y)
2025-06-24 21:40:56 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 4
2025-06-24 21:40:56 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 401.05秒
2025-06-24 21:40:56 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 3
2025-06-24 21:40:56 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 401.01秒
2025-06-24 21:40:56 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 21:40:56 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 22:06:02 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 22:06:02 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 22:06:02 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 22:06:02 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 22:06:02 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 22:06:02 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 22:06:02 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 22:06:02 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 22:06:02 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 22:06:02 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 22:06:02 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 22:06:02 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 22:06:02 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:06:02 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:06:02 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 22:06:02 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 22:06:02 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 22:06:02 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:06:02 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:06:02 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:06:02 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:06:04 | WARNING  | app.agents.web.playwright_executor:read_stderr:970 | [Playwright Error] (node:59182) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
2025-06-24 22:06:04 | WARNING  | app.agents.web.playwright_executor:read_stderr:970 | [Playwright Error] (Use `node --trace-deprecation ...` to show where the warning was created)
2025-06-24 22:06:04 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (2.7s)
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (3.5s)
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Midscene - report file updated: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-06-08-b1msi7qo.html
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report midscene_run/report[39m
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:_extract_report_path:1057 | 提取到报告路径: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-06-08-b1msi7qo.html
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:_parse_playwright_result:1011 | 找到测试报告: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-06-08-b1msi7qo.html
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1351 | 生成报告访问URL: /api/v1/web/reports/view/1c63e157-5933-460b-9095-2c57f79e6169 -> /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-06-08-b1msi7qo.html
2025-06-24 22:06:08 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 5
2025-06-24 22:06:08 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 5.31秒
2025-06-24 22:06:08 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 22:24:50 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 22:24:50 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 22:24:50 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 22:24:50 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:24:50 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 22:24:50 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:24:50 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:24:50 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:24:54 | WARNING  | app.agents.web.playwright_executor:read_stderr:970 | [Playwright Error] (node:63428) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
2025-06-24 22:24:54 | WARNING  | app.agents.web.playwright_executor:read_stderr:970 | [Playwright Error] (Use `node --trace-deprecation ...` to show where the warning was created)
2025-06-24 22:24:54 | WARNING  | app.agents.web.playwright_executor:read_stderr:970 | [Playwright Error] (node:63425) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
2025-06-24 22:24:54 | WARNING  | app.agents.web.playwright_executor:read_stderr:970 | [Playwright Error] (Use `node --trace-deprecation ...` to show where the warning was created)
2025-06-24 22:24:54 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-24 22:24:54 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (5.7s)
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (6.7s)
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Midscene - report file updated: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-25-00-o1davqhi.html
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report midscene_run/report[39m
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:_extract_report_path:1057 | 提取到报告路径: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-25-00-o1davqhi.html
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:_parse_playwright_result:1011 | 找到测试报告: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-25-00-o1davqhi.html
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1351 | 生成报告访问URL: /api/v1/web/reports/view/33663b38-8656-4c49-801c-054156252cca -> /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-25-00-o1davqhi.html
2025-06-24 22:25:00 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 6
2025-06-24 22:25:00 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 10.08秒
2025-06-24 22:25:00 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 22:25:01 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (7.0s)
2025-06-24 22:25:01 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (8.0s)
2025-06-24 22:25:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Midscene - report file updated: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-25-02-g4kluamk.html
2025-06-24 22:25:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-24 22:25:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:25:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report midscene_run/report[39m
2025-06-24 22:25:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:25:02 | INFO     | app.agents.web.playwright_executor:_extract_report_path:1057 | 提取到报告路径: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-25-02-g4kluamk.html
2025-06-24 22:25:02 | INFO     | app.agents.web.playwright_executor:_parse_playwright_result:1011 | 找到测试报告: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-25-02-g4kluamk.html
2025-06-24 22:25:02 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1351 | 生成报告访问URL: /api/v1/web/reports/view/d425d897-947e-4371-87f3-344722776259 -> /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-25-02-g4kluamk.html
2025-06-24 22:25:02 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 7
2025-06-24 22:25:02 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 11.47秒
2025-06-24 22:25:02 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 22:39:36 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 22:39:36 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 22:39:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 22:39:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 22:39:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 22:39:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 22:39:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 22:39:36 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 22:39:36 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 22:39:36 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 22:39:36 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 22:39:36 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 22:39:36 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:39:36 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:39:36 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 22:39:36 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 22:39:36 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 22:39:36 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:39:36 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:39:36 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:39:36 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:39:38 | WARNING  | app.agents.web.playwright_executor:read_stderr:970 | [Playwright Error] (node:66650) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
2025-06-24 22:39:38 | WARNING  | app.agents.web.playwright_executor:read_stderr:970 | [Playwright Error] (Use `node --trace-deprecation ...` to show where the warning was created)
2025-06-24 22:39:38 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (3.9s)
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (4.6s)
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Midscene - report file updated: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-39-43-t78wkkck.html
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report midscene_run/report[39m
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:_extract_report_path:1057 | 提取到报告路径: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-39-43-t78wkkck.html
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:_parse_playwright_result:1011 | 找到测试报告: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-39-43-t78wkkck.html
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1351 | 生成报告访问URL: /api/v1/web/reports/view/dff537dd-15e4-4310-848e-12df79ea3270 -> /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-39-43-t78wkkck.html
2025-06-24 22:39:43 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 8
2025-06-24 22:39:43 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 6.88秒
2025-06-24 22:39:43 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 22:44:37 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 22:44:37 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 22:44:37 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 22:44:37 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 22:44:37 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 22:44:37 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 22:44:37 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 22:44:37 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 22:44:37 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 22:44:37 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 22:44:37 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 22:44:37 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 22:44:37 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:44:37 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:44:37 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 22:44:37 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 22:44:37 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 22:44:37 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:44:37 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:44:37 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:44:37 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:44:41 | WARNING  | app.agents.web.playwright_executor:read_stderr:970 | [Playwright Error] (node:67727) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
2025-06-24 22:44:41 | WARNING  | app.agents.web.playwright_executor:read_stderr:970 | [Playwright Error] (Use `node --trace-deprecation ...` to show where the warning was created)
2025-06-24 22:44:41 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (3.7s)
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (4.6s)
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Midscene - report file updated: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-44-46-0tro07xt.html
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report midscene_run/report[39m
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:_extract_report_path:1057 | 提取到报告路径: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-44-46-0tro07xt.html
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:_parse_playwright_result:1011 | 找到测试报告: /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-44-46-0tro07xt.html
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1351 | 生成报告访问URL: /api/v1/web/reports/view/303fecc5-6d11-4f36-a616-e52976cbf98b -> /Users/<USER>/workspace/playwright-workspace/midscene_run/report/playwright-merged-2025-06-24_22-44-46-0tro07xt.html
2025-06-24 22:44:46 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 9
2025-06-24 22:44:46 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 8.83秒
2025-06-24 22:44:46 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 22:54:41 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 22:54:41 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 22:54:41 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 22:54:41 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 22:54:41 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 22:54:41 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 22:54:41 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 22:54:41 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 22:54:41 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 22:54:41 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 22:54:41 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 22:54:41 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 22:54:41 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:54:41 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:54:41 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 22:54:41 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 22:54:41 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 22:54:41 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:54:41 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:54:41 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:54:41 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:54:42 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-24 22:54:47 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (4.0s)
2025-06-24 22:54:47 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (4.8s)
2025-06-24 22:54:47 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-24 22:54:47 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:54:47 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report[39m
2025-06-24 22:54:47 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:54:47 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 10
2025-06-24 22:54:47 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 6.30秒
2025-06-24 22:54:47 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 22:55:52 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 22:55:52 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 22:55:52 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 22:55:52 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:55:52 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 22:55:52 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:55:52 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 22:55:52 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 22:55:54 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-24 22:55:54 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-24 22:56:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (5.0s)
2025-06-24 22:56:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (5.9s)
2025-06-24 22:56:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-24 22:56:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:56:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report[39m
2025-06-24 22:56:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:56:00 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 11
2025-06-24 22:56:00 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 7.97秒
2025-06-24 22:56:00 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 22:56:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (6.9s)
2025-06-24 22:56:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (7.7s)
2025-06-24 22:56:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-24 22:56:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:56:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report[39m
2025-06-24 22:56:02 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 22:56:02 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 12
2025-06-24 22:56:02 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 9.85秒
2025-06-24 22:56:02 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-24 23:35:11 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-24 23:35:11 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-24 23:35:11 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-24 23:35:11 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-24 23:35:11 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-24 23:35:11 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-24 23:35:11 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-24 23:35:11 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-24 23:35:11 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-24 23:35:11 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-24 23:35:11 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-24 23:35:11 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-24 23:35:11 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-24 23:35:11 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-24 23:35:11 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-24 23:35:11 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-24 23:35:11 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-24 23:35:11 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:35:11 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:35:11 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-24 23:35:11 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-24 23:35:13 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-24 23:35:18 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (4.5s)
2025-06-24 23:35:18 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (5.4s)
2025-06-24 23:35:18 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-24 23:35:18 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 23:35:18 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report[39m
2025-06-24 23:35:18 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-24 23:35:19 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 13
2025-06-24 23:35:19 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 7.36秒
2025-06-24 23:35:19 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-25 00:05:57 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-25 00:05:57 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-25 00:05:57 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-25 00:05:57 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-25 00:05:57 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-25 00:05:57 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-25 00:05:57 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-25 00:05:57 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-25 00:05:57 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-25 00:05:57 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-25 00:05:57 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-25 00:05:57 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-25 00:05:57 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-25 00:05:57 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-25 00:05:57 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-25 00:05:57 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-25 00:05:57 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-25 00:05:57 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 00:05:57 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 00:05:57 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 00:05:57 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-25 00:06:00 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-25 00:06:06 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (4.3s)
2025-06-25 00:06:06 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (6.0s)
2025-06-25 00:06:06 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-25 00:06:06 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-25 00:06:06 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report[39m
2025-06-25 00:06:06 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-25 00:06:06 | WARNING  | app.agents.web.playwright_executor:_save_test_report_to_database:1396 | 保存测试报告到数据库失败
2025-06-25 00:06:06 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 9.16秒
2025-06-25 00:06:06 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-25 08:57:38 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-25 08:57:38 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-25 08:57:38 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-25 08:57:38 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-25 08:57:38 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-25 08:57:38 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-25 08:57:38 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 08:57:38 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-25 08:57:40 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-25 08:57:40 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-25 08:57:49 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (7.5s)
2025-06-25 08:57:49 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (9.3s)
2025-06-25 08:57:49 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-25 08:57:49 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-25 08:57:49 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report[39m
2025-06-25 08:57:49 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-25 08:57:50 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 16
2025-06-25 08:57:50 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 11.08秒
2025-06-25 08:57:50 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-25 08:57:50 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (7.9s)
2025-06-25 08:57:50 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (9.8s)
2025-06-25 08:57:50 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-25 08:57:50 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-25 08:57:50 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report[39m
2025-06-25 08:57:50 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-25 08:57:50 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 17
2025-06-25 08:57:50 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 11.42秒
2025-06-25 08:57:50 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
2025-06-25 09:17:21 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-06-25 09:17:21 | INFO     | app.agents.factory:register_web_agents:201 | 开始注册Web平台智能体...
2025-06-25 09:17:21 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 图片分析智能体
2025-06-25 09:17:21 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML生成智能体
2025-06-25 09:17:21 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: YAML脚本执行智能体
2025-06-25 09:17:21 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright代码生成智能体
2025-06-25 09:17:21 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: Playwright执行智能体
2025-06-25 09:17:21 | INFO     | app.agents.factory:register_agent:183 | 注册智能体成功: 脚本数据库保存智能体
2025-06-25 09:17:21 | INFO     | app.agents.factory:register_web_agents:246 | Web平台智能体注册完成，共注册 6 个智能体
2025-06-25 09:17:21 | INFO     | app.agents.factory:register_stream_collector:331 | 流式响应收集器注册成功
2025-06-25 09:17:21 | INFO     | app.core.agents.base:__init__:38 | 初始化 Playwright执行智能体 智能体 (ID: playwright_executor)
2025-06-25 09:17:21 | INFO     | app.agents.web.playwright_executor:__init__:45 | Playwright执行智能体初始化完成: Playwright执行智能体
2025-06-25 09:17:21 | INFO     | app.agents.web.playwright_executor:__init__:46 | 执行环境路径: /Users/<USER>/workspace/playwright-workspace
2025-06-25 09:17:21 | INFO     | app.agents.web.playwright_executor:__init__:47 | 当前操作系统: /Users/<USER>/workspace/playwright-workspace
2025-06-25 09:17:21 | INFO     | app.agents.factory:create_agent:137 | 创建智能体: Playwright执行智能体
2025-06-25 09:17:21 | INFO     | app.agents.web.playwright_executor:handle_execution_request:612 | 工作空间健康检查结果: warning
2025-06-25 09:17:21 | INFO     | app.agents.web.playwright_executor:_validate_workspace:86 | Playwright工作空间验证通过
2025-06-25 09:17:21 | INFO     | app.agents.web.playwright_executor:_get_existing_script_path:593 | 找到现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:21 | INFO     | app.agents.web.playwright_executor:_execute_playwright_test:723 | 使用现有脚本文件: /Users/<USER>/workspace/playwright-workspace/e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:21 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:852 | 执行命令: npx playwright test e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts
2025-06-25 09:17:21 | INFO     | app.agents.web.playwright_executor:_run_playwright_test:853 | 工作目录: /Users/<USER>/workspace/playwright-workspace
2025-06-25 09:17:23 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] Running 1 test using 1 worker
2025-06-25 09:17:35 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] ✓  1 [chromium] › e2e/AI生成PLAYWRIGHT脚本_2025_6_24_20250624_220602.spec.ts:14:7 › 真实多页面测试套件 › 测试页面1 - 慢病数据管理软件 (10.8s)
2025-06-25 09:17:35 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] 1 passed (11.7s)
2025-06-25 09:17:35 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] To open last HTML report run:
2025-06-25 09:17:35 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-25 09:17:35 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m  npx playwright show-report[39m
2025-06-25 09:17:35 | INFO     | app.agents.web.playwright_executor:read_stdout:961 | [Playwright] [36m[39m
2025-06-25 09:17:35 | INFO     | app.agents.web.playwright_executor:_save_test_report_to_database:1390 | 测试报告已保存到数据库: 18
2025-06-25 09:17:35 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Playwright执行智能体] playwright_execution 耗时: 14.06秒
2025-06-25 09:17:35 | INFO     | app.agents.factory:clear_registered_agents:375 | 已清空智能体注册记录
