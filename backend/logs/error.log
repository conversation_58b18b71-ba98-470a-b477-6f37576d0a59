2025-06-27 14:04:54 | ERROR    | app.rag.vector_store:connect:58 | 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on 127.0.0.1:19530, illegal connection params or server unavailable)>
2025-06-27 14:04:54 | ERROR    | app.rag.rag_system:initialize:109 | RAG系统初始化失败: <MilvusException: (code=2, message=Fail connecting to server on 127.0.0.1:19530, illegal connection params or server unavailable)>
2025-06-27 14:04:54 | ERROR    | app.main:init_rag_system:229 | RAG系统初始化失败: <MilvusException: (code=2, message=Fail connecting to server on 127.0.0.1:19530, illegal connection params or server unavailable)>
2025-06-27 14:06:14 | ERROR    | app.rag.vector_store:_create_index:193 | 创建索引失败: <MilvusException: (code=1100, message=index type not match: invalid parameter[expected=Trie][actual=TRIE])>
2025-06-27 14:06:14 | ERROR    | app.rag.vector_store:initialize_collection:155 | 创建集合失败: <MilvusException: (code=1100, message=index type not match: invalid parameter[expected=Trie][actual=TRIE])>
2025-06-27 14:06:14 | ERROR    | app.rag.retrieval:initialize:99 | 检索服务初始化失败: <MilvusException: (code=1100, message=index type not match: invalid parameter[expected=Trie][actual=TRIE])>
2025-06-27 14:06:14 | ERROR    | app.rag.rag_system:initialize:109 | RAG系统初始化失败: <MilvusException: (code=1100, message=index type not match: invalid parameter[expected=Trie][actual=TRIE])>
2025-06-27 14:06:14 | ERROR    | app.main:init_rag_system:229 | RAG系统初始化失败: <MilvusException: (code=1100, message=index type not match: invalid parameter[expected=Trie][actual=TRIE])>
