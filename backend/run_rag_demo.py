#!/usr/bin/env python3
"""
RAG系统演示脚本

运行RAG系统的示例和测试，展示完整功能
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from app.core.config import get_settings


def setup_demo_logging():
    """设置演示日志"""
    logger.remove()  # 移除默认处理器
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO",
        colorize=True
    )
    
    # 添加文件输出
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logger.add(
        "logs/rag_demo.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="10 MB",
        retention="7 days"
    )


def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    RAG系统演示程序                           ║
║                                                              ║
║  基于UI自动化测试项目的检索增强生成系统                      ║
║  支持文档处理、向量化、存储和智能检索                        ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_prerequisites():
    """检查前置条件"""
    logger.info("检查系统前置条件...")
    
    settings = get_settings()
    
    # 检查RAG是否启用
    if not settings.RAG_ENABLED:
        logger.error("RAG系统未启用，请在配置中设置 RAG_ENABLED=true")
        return False
    
    # 检查必要的目录
    required_dirs = ["logs", "temp"]
    for dir_name in required_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    logger.info("✅ 前置条件检查完成")
    return True


async def run_system_check():
    """运行系统检查"""
    logger.info("执行系统健康检查...")
    
    try:
        from app.rag.rag_system import RAGSystem
        
        # 创建RAG系统实例
        rag_system = RAGSystem()
        
        # 初始化系统
        await rag_system.initialize()
        
        # 健康检查
        health = await rag_system.health_check()
        
        logger.info(f"系统状态: {health.get('status', 'unknown')}")
        
        components = health.get("components", {})
        for name, status in components.items():
            component_status = status.get("status", "unknown")
            message = status.get("message", "")
            logger.info(f"  {name}: {component_status} - {message}")
        
        # 清理
        await rag_system.cleanup()
        
        return health.get("status") == "healthy"
        
    except Exception as e:
        logger.error(f"系统检查失败: {e}")
        return False


async def run_examples():
    """运行使用示例"""
    logger.info("开始运行RAG系统使用示例...")
    
    try:
        from app.rag.examples import RAGExamples
        
        examples = RAGExamples()
        await examples.run_all_examples()
        
        logger.info("✅ 示例运行完成")
        return True
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        return False


async def run_tests():
    """运行测试用例"""
    logger.info("开始运行RAG系统测试用例...")
    
    try:
        from app.rag.tests import RAGSystemTests
        
        tests = RAGSystemTests()
        await tests.run_all_tests()
        
        logger.info("✅ 测试运行完成")
        return True
        
    except Exception as e:
        logger.error(f"测试运行失败: {e}")
        return False


async def run_performance_benchmark():
    """运行性能基准测试"""
    logger.info("开始性能基准测试...")
    
    try:
        from app.rag.rag_system import RAGSystem
        import time
        
        rag_system = RAGSystem()
        await rag_system.initialize()
        await rag_system.setup_collection(drop_existing=True)
        
        # 测试数据
        test_docs = [
            f"这是第{i}个性能测试文档，包含一些测试内容用于评估系统性能。" * 20
            for i in range(20)
        ]
        
        # 批量添加测试
        logger.info("测试批量文档添加性能...")
        start_time = time.time()
        
        for i, doc in enumerate(test_docs):
            result = await rag_system.add_text_directly(doc, f"perf_doc_{i}")
            if not result.success:
                logger.warning(f"文档 {i} 添加失败")
        
        add_time = time.time() - start_time
        avg_add_time = add_time / len(test_docs)
        
        logger.info(f"批量添加完成: {len(test_docs)} 个文档, 总耗时: {add_time:.2f}s, 平均: {avg_add_time:.3f}s/文档")
        
        # 批量搜索测试
        test_queries = [
            "性能测试",
            "测试文档",
            "系统评估",
            "文档内容",
            "批量处理"
        ]
        
        logger.info("测试批量搜索性能...")
        start_time = time.time()
        
        for query in test_queries:
            response = await rag_system.search(query)
            logger.debug(f"查询 '{query}' 返回 {len(response.retrieved_chunks)} 个结果")
        
        search_time = time.time() - start_time
        avg_search_time = search_time / len(test_queries)
        
        logger.info(f"批量搜索完成: {len(test_queries)} 个查询, 总耗时: {search_time:.2f}s, 平均: {avg_search_time:.3f}s/查询")
        
        # 系统统计
        stats = await rag_system.get_system_stats()
        if "vector_store" in stats:
            vector_stats = stats["vector_store"]
            logger.info(f"向量数据库统计: {vector_stats.get('row_count', 0)} 条记录")
        
        await rag_system.cleanup()
        
        # 性能评估
        performance_ok = avg_add_time < 5.0 and avg_search_time < 2.0
        
        if performance_ok:
            logger.info("✅ 性能基准测试通过")
        else:
            logger.warning("⚠️ 性能基准测试未达到预期")
        
        return performance_ok
        
    except Exception as e:
        logger.error(f"性能基准测试失败: {e}")
        return False


def show_menu():
    """显示菜单"""
    menu = """
请选择要执行的操作:

1. 系统健康检查
2. 运行使用示例
3. 运行测试用例
4. 运行性能基准测试
5. 运行完整演示 (包含以上所有)
6. 退出

请输入选择 (1-6): """
    
    return input(menu).strip()


async def run_full_demo():
    """运行完整演示"""
    logger.info("开始完整RAG系统演示...")
    
    success_count = 0
    total_tests = 4
    
    # 1. 系统检查
    if await run_system_check():
        success_count += 1
    
    # 2. 运行示例
    if await run_examples():
        success_count += 1
    
    # 3. 运行测试
    if await run_tests():
        success_count += 1
    
    # 4. 性能测试
    if await run_performance_benchmark():
        success_count += 1
    
    logger.info(f"完整演示结果: {success_count}/{total_tests} 项测试通过")
    
    if success_count == total_tests:
        logger.info("🎉 RAG系统演示完全成功！")
    else:
        logger.warning(f"⚠️ 部分测试失败，请检查日志")
    
    return success_count == total_tests


async def main():
    """主函数"""
    setup_demo_logging()
    print_banner()
    
    # 检查前置条件
    if not check_prerequisites():
        logger.error("前置条件检查失败，程序退出")
        return 1
    
    while True:
        try:
            choice = show_menu()
            
            if choice == "1":
                await run_system_check()
            elif choice == "2":
                await run_examples()
            elif choice == "3":
                await run_tests()
            elif choice == "4":
                await run_performance_benchmark()
            elif choice == "5":
                await run_full_demo()
            elif choice == "6":
                logger.info("程序退出")
                break
            else:
                print("无效选择，请重新输入")
                continue
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            logger.info("用户中断，程序退出")
            break
        except Exception as e:
            logger.error(f"程序执行错误: {e}")
            input("\n按回车键继续...")
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
